import { AppLayout } from "@/components/layout"

export default function EcommercePage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Ecommerce</h1>
          <p className="text-muted-foreground">
            Manage your online store and products.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Products</h3>
            <p className="text-sm text-muted-foreground">Manage your product catalog</p>
          </div>
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Orders</h3>
            <p className="text-sm text-muted-foreground">Track and manage orders</p>
          </div>
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Customers</h3>
            <p className="text-sm text-muted-foreground">Customer management</p>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
