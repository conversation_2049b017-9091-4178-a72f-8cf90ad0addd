"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import {
  BarChart3,
  Calendar,
  ChevronDown,
  ChevronRight,
  CreditCard,
  FileText,
  LayoutDashboard,
  ShoppingCart,
  TrendingUp,
  Users,
  Package,
  Cloud,
  User,
  CheckSquare,
  FileBarChart,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

// Navigation items data
const navigationItems = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    url: "/dashboard",
    isActive: true,
    items: [
      { title: "Analytics", url: "/dashboard/analytics" },
      { title: "Reports", url: "/dashboard/reports" },
    ],
  },
  {
    title: "Ecommerce",
    icon: ShoppingCart,
    url: "/ecommerce",
  },
  {
    title: "Analytics",
    icon: BarChart3,
    url: "/analytics",
    badge: "PRO",
  },
  {
    title: "Marketing",
    icon: TrendingUp,
    url: "/marketing",
    badge: "PRO",
  },
  {
    title: "CRM",
    icon: Users,
    url: "/crm",
    badge: "PRO",
  },
  {
    title: "Stocks",
    icon: Package,
    url: "/stocks",
    badges: ["NEW", "PRO"],
  },
  {
    title: "SaaS",
    icon: Cloud,
    url: "/saas",
    badges: ["NEW", "PRO"],
  },
  {
    title: "Calendar",
    icon: Calendar,
    url: "/calendar",
  },
  {
    title: "User Profile",
    icon: User,
    url: "/profile",
  },
  {
    title: "Task",
    icon: CheckSquare,
    url: "/tasks",
    items: [
      { title: "All Tasks", url: "/tasks/all" },
      { title: "My Tasks", url: "/tasks/my" },
      { title: "Completed", url: "/tasks/completed" },
    ],
  },
  {
    title: "Forms",
    icon: FileBarChart,
    url: "/forms",
    items: [
      { title: "Form Elements", url: "/forms/elements" },
      { title: "Form Layout", url: "/forms/layout" },
      { title: "Form Validation", url: "/forms/validation" },
    ],
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-4 py-4">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
            <BarChart3 className="h-5 w-5 text-white" />
          </div>
          <span className="text-lg font-semibold">Clover ERP</span>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider px-4 py-2">
            MENU
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => {
                const isActive = pathname === item.url || pathname.startsWith(item.url + "/")
                
                if (item.items) {
                  return (
                    <SidebarMenuItem key={item.title}>
                      <Collapsible defaultOpen={isActive}>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton
                            className={cn(
                              "w-full justify-between group",
                              isActive && "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-400"
                            )}
                          >
                            <div className="flex items-center gap-2">
                              <item.icon className="h-4 w-4" />
                              <span>{item.title}</span>
                            </div>
                            <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {item.items.map((subItem) => (
                              <SidebarMenuSubItem key={subItem.title}>
                                <SidebarMenuSubButton asChild>
                                  <Link href={subItem.url}>
                                    <span>{subItem.title}</span>
                                  </Link>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </Collapsible>
                    </SidebarMenuItem>
                  )
                }

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      className={cn(
                        isActive && "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-400"
                      )}
                    >
                      <Link href={item.url} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <item.icon className="h-4 w-4" />
                          <span>{item.title}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {item.badges ? (
                            item.badges.map((badge) => (
                              <Badge
                                key={badge}
                                variant={badge === "NEW" ? "default" : "secondary"}
                                className={cn(
                                  "text-xs px-1.5 py-0.5 h-5",
                                  badge === "NEW" && "bg-blue-600 text-white",
                                  badge === "PRO" && "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
                                )}
                              >
                                {badge}
                              </Badge>
                            ))
                          ) : item.badge ? (
                            <Badge
                              variant="secondary"
                              className="text-xs px-1.5 py-0.5 h-5 bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
                            >
                              {item.badge}
                            </Badge>
                          ) : null}
                        </div>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
